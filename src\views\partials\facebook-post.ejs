<!-- Enhanced Facebook-style Post Component -->
<div class="fb-post" data-post-id="<%= post.id %>">
  <!-- Post Header -->
  <div class="fb-post-header">
    <div class="fb-post-avatar">
      <a href="/profile/user/<%= post.userId %>" class="fb-avatar-link" title="View <%= post.userName || 'User' %>'s profile">
        <% if (post.userPhotoURL) { %>
          <img src="<%= post.userPhotoURL %>" alt="<%= post.userName || 'User' %>" class="fb-avatar">
        <% } else { %>
          <div class="fb-avatar fb-avatar-placeholder">
            <i class="bi bi-person-fill"></i>
          </div>
        <% } %>
      </a>
    </div>
    <div class="fb-post-user-info">
      <div class="fb-post-username">
        <a href="/profile/user/<%= post.userId %>" class="text-decoration-none" title="View <%= post.userName || 'User' %>'s profile">
          <%= post.userName || 'Anonymous' %>
        </a>
      </div>
      <div class="fb-post-time">
        <span class="time-ago" data-time="<%= post.createdAt %>">
          <%= new Date(post.createdAt).toLocaleDateString() %>
        </span>
        <% if (post.category) { %>
          · <span class="badge bg-light text-dark ms-1"><%= post.category %></span>
        <% } %>
      </div>
    </div>
    <div class="fb-post-options">
      <i class="bi bi-three-dots"></i>
    </div>
  </div>

  <!-- Post Content -->
  <div class="fb-post-content">
    <% if (post.title || post.description) { %>
      <div class="fb-post-text">
        <% if (post.title) { %>
          <h5><%= post.title %></h5>
        <% } %>
        <% if (post.description) { %>
          <p><%= post.description %></p>
        <% } %>
      </div>
    <% } %>

    <!-- Media Content -->
    <% if (post.fileUrl) { %>
      <%
        // Determine file type
        let isImage = false;
        let isVideo = false;
        let isPdf = false;

        if (post.fileType) {
          isImage = post.fileType.startsWith('image/');
          isVideo = post.fileType.startsWith('video/');
          isPdf = post.fileType === 'application/pdf';
        } else {
          const url = post.fileUrl.toLowerCase();
          isImage = url.includes('.jpg') || url.includes('.jpeg') || url.includes('.png') || url.includes('.gif') || url.includes('.webp') || url.includes('unsplash.com');
          isVideo = url.includes('.mp4') || url.includes('.webm') || url.includes('.ogg');
          isPdf = url.includes('.pdf');
        }
      %>

      <% if (isImage) { %>
        <img src="<%= post.fileUrl %>" class="fb-post-image" alt="<%= post.title || 'Post image' %>" loading="lazy">
      <% } else if (isVideo) { %>
        <div class="fb-post-file-container video">
          <i class="bi bi-film"></i>
          <div>Video Content</div>
          <a href="<%= post.fileUrl %>" class="btn btn-sm btn-outline-primary mt-2" target="_blank">
            <i class="bi bi-play-fill"></i> Play Video
          </a>
        </div>
      <% } else if (isPdf) { %>
        <div class="fb-post-file-container pdf">
          <i class="bi bi-file-pdf"></i>
          <div>PDF Document</div>
          <a href="<%= post.fileUrl %>" class="btn btn-sm btn-outline-primary mt-2" target="_blank">
            <i class="bi bi-eye"></i> View PDF
          </a>
        </div>
      <% } else { %>
        <div class="fb-post-file-container file">
          <i class="bi bi-file-earmark"></i>
          <div>Document</div>
          <a href="<%= post.fileUrl %>" class="btn btn-sm btn-outline-primary mt-2" target="_blank">
            <i class="bi bi-download"></i> Download
          </a>
        </div>
      <% } %>
    <% } %>
  </div>

  <!-- Post Stats -->
  <div class="fb-post-stats" data-post-id="<%= post.id %>">
    <div>
      <%
        // Ensure likes array exists and calculate count
        const statsLikesArray = Array.isArray(post.likes) ? post.likes : [];
        const likeCount = post.likeCount || statsLikesArray.length || 0;
      %>
      <i class="bi bi-hand-thumbs-up-fill"></i>
      <span><%= likeCount %></span>
    </div>
    <div>
      <%
        // Ensure comments array exists and calculate count
        const commentsArray = Array.isArray(post.comments) ? post.comments : [];
        const commentCount = post.commentCount || commentsArray.length || 0;
        const shareCount = post.shareCount || 0;
      %>
      <span><%= commentCount %></span> comment<%= commentCount !== 1 ? 's' : '' %> ·
      <span><%= shareCount %></span> share<%= shareCount !== 1 ? 's' : '' %>
    </div>
  </div>

  <!-- Post Actions -->
  <div class="fb-post-actions">
    <%
      // Check if current user has liked this post
      const currentUserId = typeof user !== 'undefined' && user ? user.uid : null;
      const postLikesArray = Array.isArray(post.likes) ? post.likes : [];
      const isLiked = currentUserId && postLikesArray.includes(currentUserId);
    %>
    <div class="fb-post-action-button like <%= isLiked ? 'liked' : '' %>"
         data-item-id="<%= post.id %>"
         onclick="handleLike('<%= post.id %>')"
         style="<%= isLiked ? 'color: #1877f2;' : '' %>">
      <i class="bi bi-hand-thumbs-up<%= isLiked ? '-fill' : '' %>"></i>
      <span><%= isLiked ? 'Liked' : 'Like' %></span>
    </div>
    <div class="fb-post-action-button comment" data-item-id="<%= post.id %>" onclick="focusCommentInput('<%= post.id %>')">
      <i class="bi bi-chat"></i>
      <span>Comment</span>
    </div>
    <div class="fb-post-action-button share" data-item-id="<%= post.id %>" onclick="handleShare('<%= post.id %>')">
      <i class="bi bi-share"></i>
      <span>Share</span>
    </div>
  </div>

  <!-- Comments Section -->
  <div class="fb-comments-section" id="comments-<%= post.id %>">
    <!-- Existing Comments -->
    <% if (post.comments && post.comments.length > 0) { %>
      <div class="fb-existing-comments">
        <% post.comments.forEach(comment => { %>
          <div class="fb-comment" data-comment-id="<%= comment.id %>">
            <div class="fb-comment-avatar">
              <% if (comment.userPhotoURL) { %>
                <img src="<%= comment.userPhotoURL %>" alt="<%= comment.userName %>" class="fb-comment-avatar-img">
              <% } else { %>
                <div class="fb-comment-avatar-placeholder">
                  <i class="bi bi-person-fill"></i>
                </div>
              <% } %>
            </div>
            <div class="fb-comment-content">
              <div class="fb-comment-bubble">
                <div class="fb-comment-author">
                  <a href="/profile/user/<%= comment.userId %>" class="fb-comment-author-link">
                    <%= comment.userName %>
                  </a>
                </div>
                <div class="fb-comment-text"><%= comment.text %></div>
              </div>
              <div class="fb-comment-actions">
                <span class="fb-comment-time time-ago" data-time="<%= comment.createdAt %>">
                  <%= new Date(comment.createdAt).toLocaleDateString() %>
                </span>
                <span class="fb-comment-action" onclick="likeComment('<%= comment.id %>')">Like</span>
                <span class="fb-comment-action" onclick="replyToComment('<%= comment.id %>')">Reply</span>
              </div>
            </div>
          </div>
        <% }); %>
      </div>
    <% } %>

    <!-- Comment Input Section -->
    <div class="fb-comment-input-section" id="comment-input-<%= post.id %>">
      <div class="fb-comment-input-container">
        <div class="fb-comment-input-avatar">
          <% if (typeof user !== 'undefined' && user && user.photoURL) { %>
            <img src="<%= user.photoURL %>" alt="<%= user.displayName %>" class="fb-comment-avatar-img">
          <% } else { %>
            <div class="fb-comment-avatar-placeholder">
              <i class="bi bi-person-fill"></i>
            </div>
          <% } %>
        </div>
        <div class="fb-comment-input-wrapper">
          <textarea
            class="fb-comment-input"
            id="comment-text-<%= post.id %>"
            placeholder="Write a comment..."
            rows="1"
            data-post-id="<%= post.id %>"
          ></textarea>
          <div class="fb-comment-input-actions">
            <button type="button" class="fb-comment-emoji-btn" title="Add emoji">
              <i class="bi bi-emoji-smile"></i>
            </button>
            <button type="button" class="fb-comment-photo-btn" title="Add photo">
              <i class="bi bi-camera"></i>
            </button>
            <button
              type="button"
              class="fb-comment-send-btn"
              onclick="submitComment('<%= post.id %>')"
              title="Send comment"
            >
              <i class="bi bi-send"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
